#!/usr/bin/env python3
"""
简单的AI聊天测试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.ai_client import OpenRouterClient

async def test_ai_chat():
    """测试AI聊天功能"""
    print("🧪 测试AI聊天功能...")
    print("=" * 50)
    
    # 初始化AI客户端
    ai_client = OpenRouterClient()
    
    # 测试简单对话
    test_message = "你好！我是你的主人，请用可爱的语气回复我。"
    
    try:
        print(f"📤 发送消息: {test_message}")
        
        # 构建宠物数据
        pet_data = {
            "name": "小智",
            "breed": "金毛寻回犬",
            "personality": "活泼友善，聪明好学",
            "current_mood": "happy",
            "traits": ["活泼", "友善", "聪明", "忠诚"]
        }

        # 生成AI回复
        response = await ai_client.generate_enhanced_pet_response(
            user_message=test_message,
            pet_data=pet_data,
            conversation_history=[],
            context_data={}
        )
        
        print(f"📥 AI回复: {response['content']}")
        print(f"🎭 情绪: {response.get('mood', 'unknown')}")
        print(f"🎯 置信度: {response.get('confidence', 0)}")
        print(f"⚡ 响应时间: {response.get('response_time', 0)}ms")
        
        print("\n✅ AI聊天功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ AI聊天功能测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始AI聊天测试...")
    
    success = await test_ai_chat()
    
    if success:
        print("\n🎉 所有测试通过！AI聊天功能正常工作。")
    else:
        print("\n💥 测试失败！请检查AI配置。")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
